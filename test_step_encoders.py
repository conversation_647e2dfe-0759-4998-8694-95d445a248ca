#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分步编码器功能测试脚本
"""

import os
import sys
import tempfile
import json

# 添加模块路径
sys.path.append(os.path.dirname(__file__))

from modules.video_composer.ffmpeg_composer import FFmpegComposer

def test_step_encoders():
    """测试分步编码器功能"""
    print("=== 分步编码器功能测试 ===\n")
    
    # 创建FFmpeg合成器实例
    composer = FFmpegComposer()
    
    # 检查FFmpeg是否可用
    if not composer.is_available():
        print("❌ FFmpeg不可用，请检查FFmpeg安装")
        return False
    
    print("✅ FFmpeg可用")
    
    # 测试编码器检测
    print("\n1. 检测可用编码器...")
    encoders = ['libx264', 'h264_nvenc', 'h264_qsv', 'h264_amf']
    available_encoders = []
    
    for encoder in encoders:
        if composer._check_encoder_available(encoder):
            available_encoders.append(encoder)
            print(f"  ✅ {encoder} - 可用")
        else:
            print(f"  ❌ {encoder} - 不可用")
    
    if not available_encoders:
        print("❌ 没有找到可用的编码器")
        return False
    
    # 测试CUDA支持
    print("\n2. 检测CUDA支持...")
    cuda_supported = composer._check_cuda_support()
    if cuda_supported:
        print("  ✅ CUDA硬件加速支持")
    else:
        print("  ❌ CUDA硬件加速不支持")
    
    # 测试编码器性能
    print("\n3. 测试编码器性能...")
    
    def progress_callback(msg):
        print(f"  进度: {msg}")
    
    try:
        results = composer.test_encoder_performance(progress_callback=progress_callback)
        
        if "error" in results:
            print(f"  ❌ 性能测试失败: {results['error']}")
            return False
        
        print("\n  性能测试结果:")
        performance_list = []
        for encoder, result in results.items():
            if result['available'] and result['time'] is not None:
                performance_list.append((encoder, result['time'], result['name']))
                print(f"    {result['name']}: {result['time']:.2f}秒")
            else:
                print(f"    {result['name']}: 不可用或失败")
        
        # 按性能排序
        performance_list.sort(key=lambda x: x[1])
        
        if performance_list:
            fastest = performance_list[0]
            print(f"\n  🏆 最快编码器: {fastest[2]} ({fastest[1]:.2f}秒)")
        
    except Exception as e:
        print(f"  ❌ 性能测试异常: {str(e)}")
        return False
    
    # 测试分步编码器设置
    print("\n4. 测试分步编码器设置...")
    
    test_settings = {
        'subtitle_encoder': 'libx264',
        'audio_merge_encoder': 'h264_nvenc' if 'h264_nvenc' in available_encoders else 'libx264',
        'final_encoder': available_encoders[0],
        'use_hardware_acceleration': True,
        'enable_parallel': True,
        'max_threads': 4,
        'fast_mode': True
    }
    
    print("  测试设置:")
    for step, encoder in [
        ('字幕合成', test_settings['subtitle_encoder']),
        ('音频合并', test_settings['audio_merge_encoder']),
        ('最终输出', test_settings['final_encoder'])
    ]:
        print(f"    {step}: {encoder}")
    
    # 测试编码器设置获取
    print("\n5. 测试编码器设置获取...")
    
    for step_name, encoder_key in [
        ('字幕合成', 'subtitle_encoder'),
        ('音频合并', 'audio_merge_encoder'),
        ('最终输出', 'final_encoder')
    ]:
        step_settings = test_settings.copy()
        step_settings['encoder'] = test_settings[encoder_key]
        
        try:
            codec, params = composer._get_optimal_encoder_settings(step_settings)
            print(f"  {step_name}:")
            print(f"    编码器: {codec}")
            print(f"    参数: {' '.join(params)}")
        except Exception as e:
            print(f"  ❌ {step_name}设置获取失败: {str(e)}")
    
    print("\n=== 测试完成 ===")
    return True

def test_optimization_algorithm():
    """测试编码器优化算法"""
    print("\n=== 编码器优化算法测试 ===\n")
    
    # 模拟性能测试结果
    mock_results = [
        ('libx264', 8.5, '软件编码器 (x264)'),
        ('h264_nvenc', 3.2, 'NVIDIA硬件编码器'),
        ('h264_qsv', 4.1, 'Intel硬件编码器'),
        ('h264_amf', 5.8, 'AMD硬件编码器')
    ]
    
    print("模拟性能测试结果:")
    for encoder, time_cost, name in mock_results:
        print(f"  {name}: {time_cost:.2f}秒")
    
    # 测试优化算法
    def get_optimized_allocation(available_encoders):
        """模拟优化分配算法"""
        settings = {
            'subtitle': 'auto',
            'audio_merge': 'auto', 
            'final': 'auto'
        }
        
        if not available_encoders:
            return settings
        
        # 获取最快的编码器
        fastest_encoder = available_encoders[0][0]
        
        # 检查是否有硬件编码器
        hardware_encoders = [enc for enc in available_encoders if any(hw in enc[0] for hw in ['nvenc', 'qsv', 'amf'])]
        software_encoders = [enc for enc in available_encoders if enc[0] == 'libx264']
        
        if hardware_encoders and software_encoders:
            # 有硬件和软件编码器，智能分配
            hw_fastest = hardware_encoders[0][0]
            sw_fastest = software_encoders[0][0]
            
            # 字幕合成：通常硬件编码器更快
            settings['subtitle'] = hw_fastest
            
            # 音频合并：硬件编码器处理音频混音更快
            settings['audio_merge'] = hw_fastest
            
            # 最终输出：使用最快的编码器
            settings['final'] = fastest_encoder
            
        elif hardware_encoders:
            # 只有硬件编码器
            settings['subtitle'] = fastest_encoder
            settings['audio_merge'] = fastest_encoder
            settings['final'] = fastest_encoder
            
        elif software_encoders:
            # 只有软件编码器
            settings['subtitle'] = 'libx264'
            settings['audio_merge'] = 'libx264'
            settings['final'] = 'libx264'
        
        return settings
    
    # 按性能排序
    mock_results.sort(key=lambda x: x[1])
    
    # 获取优化设置
    optimized = get_optimized_allocation(mock_results)
    
    print("\n优化后的编码器分配:")
    for step, encoder in optimized.items():
        # 找到编码器的显示名称
        encoder_name = encoder
        for enc_code, _, enc_name in mock_results:
            if enc_code == encoder:
                encoder_name = enc_name
                break
        print(f"  {step}: {encoder_name}")
    
    print("\n优化策略说明:")
    print("  - 字幕合成: 优先使用硬件编码器（渲染速度快）")
    print("  - 音频合并: 优先使用硬件编码器（音频混音处理快）")
    print("  - 最终输出: 使用最快的编码器（整体效率最高）")
    
    print("\n=== 优化算法测试完成 ===")

if __name__ == "__main__":
    print("分步编码器功能测试\n")
    
    try:
        # 基础功能测试
        success = test_step_encoders()
        
        if success:
            # 优化算法测试
            test_optimization_algorithm()
            print("\n🎉 所有测试完成！")
        else:
            print("\n❌ 基础功能测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
