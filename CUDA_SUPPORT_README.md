# CUDA硬件加速支持说明

## 概述
CUDA硬件加速可以显著提升视频处理和字幕合成的速度，特别是在处理高分辨率视频时。但是，启用CUDA加速需要满足特定的硬件和软件要求。

## 硬件要求

### NVIDIA显卡
- **支持的显卡**：GTX 600系列及以上（推荐GTX 1060或更高）
- **显存要求**：至少2GB显存，推荐4GB以上
- **架构支持**：Kepler、Maxwell、Pascal、Turing、Amper<PERSON>、Ada Lovelace

### 常见支持的显卡型号
- **GTX系列**：GTX 660, 670, 680, 750, 950, 960, 1050, 1060, 1070, 1080等
- **RTX系列**：RTX 2060, 2070, 2080, 3060, 3070, 3080, 4060, 4070, 4080, 4090等
- **专业卡**：Quadro系列、Tesla系列

## 软件要求

### 1. NVIDIA驱动程序
- **最低版本**：建议使用最新的NVIDIA驱动
- **下载地址**：https://www.nvidia.com/drivers/
- **检查方法**：运行 `nvidia-smi` 命令查看驱动版本

### 2. CUDA Toolkit（可选）
- **说明**：虽然不是必需的，但安装CUDA Toolkit可以提供更好的兼容性
- **下载地址**：https://developer.nvidia.com/cuda-downloads
- **推荐版本**：CUDA 11.0或更高版本

### 3. 支持CUDA的FFmpeg
这是最关键的要求！大多数预编译的FFmpeg版本**不包含**CUDA支持。

#### 获取支持CUDA的FFmpeg的方法：

**方法1：下载预编译版本**
- 从 https://github.com/BtbN/FFmpeg-Builds/releases 下载包含CUDA支持的版本
- 选择带有 "gpl-shared" 或 "lgpl-shared" 标记的版本
- 解压后替换原有的FFmpeg文件

**方法2：使用Conda安装**
```bash
conda install ffmpeg -c conda-forge
```

**方法3：自行编译（高级用户）**
- 需要安装CUDA Toolkit和相关开发工具
- 编译时启用 `--enable-cuda-nvcc` 和 `--enable-nvenc` 选项

## 检测CUDA支持

### 在应用中检测
1. 打开视频合成模块
2. 在"性能设置"区域点击"检测CUDA"按钮
3. 查看检测结果：
   - ✓ 支持CUDA加速：可以使用CUDA加速功能
   - ✗ 不支持CUDA：需要检查上述要求

### 手动检测方法

**检查FFmpeg CUDA支持**：
```bash
ffmpeg -hwaccels
```
输出中应该包含 `cuda`

**检查NVENC编码器**：
```bash
ffmpeg -encoders | grep nvenc
```
应该显示类似：
```
V..... h264_nvenc         NVIDIA NVENC H.264 encoder
V..... hevc_nvenc         NVIDIA NVENC hevc encoder
```

**测试CUDA功能**：
```bash
ffmpeg -f lavfi -i testsrc=duration=10:size=1280x720:rate=30 -hwaccel cuda -c:v h264_nvenc -t 5 test_cuda.mp4
```

## 性能对比

### 典型性能提升（相对于CPU编码）
- **1080p视频**：2-5倍速度提升
- **4K视频**：3-8倍速度提升
- **字幕合成**：1.5-3倍速度提升

### 实际测试结果示例
| 编码器 | 1080p (30fps, 10秒) | 4K (30fps, 10秒) |
|--------|-------------------|------------------|
| libx264 (CPU) | 15-25秒 | 60-120秒 |
| h264_nvenc (CUDA) | 3-8秒 | 15-30秒 |

## 故障排除

### 常见问题

**1. "CUDA不可用"错误**
- 检查NVIDIA驱动是否正确安装
- 确认FFmpeg版本支持CUDA
- 重启计算机后重试

**2. "编码器不可用"错误**
- 显卡可能不支持硬件编码
- FFmpeg版本可能不包含NVENC支持
- 尝试更新显卡驱动

**3. 编码失败或质量问题**
- 显存不足，尝试降低分辨率或关闭其他占用显存的程序
- 使用软件编码器作为备选方案

### 解决方案

**如果CUDA检测失败**：
1. 确认硬件支持（NVIDIA显卡）
2. 更新显卡驱动到最新版本
3. 下载支持CUDA的FFmpeg版本
4. 重启应用程序重新检测

**如果性能提升不明显**：
1. 检查是否有其他程序占用GPU
2. 确认使用的是硬件编码器而非软件编码器
3. 调整编码参数（预设、质量等）

## 备选方案

如果无法启用CUDA支持，应用程序会自动回退到：
1. **其他硬件编码器**：Intel QSV、AMD AMF
2. **优化的软件编码器**：多线程libx264
3. **兼容性模式**：最基本的编码设置

## 总结

CUDA硬件加速可以显著提升视频处理性能，但需要：
- NVIDIA显卡（GTX 600系列及以上）
- 最新的NVIDIA驱动
- 支持CUDA的FFmpeg版本

如果您的系统不支持CUDA，应用程序仍然可以正常工作，只是处理速度会相对较慢。建议根据实际需求和硬件配置选择合适的编码器。
