#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合并功能时输出单独分集视频的功能
"""

import os
import sys
import tempfile
import json

# 添加模块路径
sys.path.append(os.path.dirname(__file__))

def test_individual_videos_output():
    """测试单独分集视频输出功能"""
    print("=== 单独分集视频输出功能测试 ===\n")
    
    # 模拟处理结果
    mock_results = [
        {
            'success': True,
            'novel_name': '测试小说1',
            'output_path': '/output/测试小说1_完整版.mp4',
            'individual_videos': [
                {
                    'episode': 1,
                    'path': '/output/测试小说1_01.mp4',
                    'name': '测试小说1_01.mp4'
                },
                {
                    'episode': 2,
                    'path': '/output/测试小说1_02.mp4',
                    'name': '测试小说1_02.mp4'
                },
                {
                    'episode': 3,
                    'path': '/output/测试小说1_03.mp4',
                    'name': '测试小说1_03.mp4'
                }
            ],
            'mode': 'create_merged',
            'total_episodes': 3
        },
        {
            'success': True,
            'novel_name': '测试小说2',
            'main_output_path': '/output/测试小说2_合并版.mp4',
            'individual_videos': [
                {
                    'episode': 1,
                    'path': '/output/测试小说2_01.mp4',
                    'name': '测试小说2_01.mp4'
                },
                {
                    'episode': 2,
                    'path': '/output/测试小说2_02.mp4',
                    'name': '测试小说2_02.mp4'
                }
            ],
            'mode': 'merge_with_first',
            'total_episodes': 2
        }
    ]
    
    # 模拟设置
    settings = {
        'enable_merge': True
    }
    
    print("模拟处理结果:")
    for i, result in enumerate(mock_results, 1):
        print(f"\n小说 {i}: {result['novel_name']}")
        print(f"  模式: {result['mode']}")
        print(f"  总集数: {result['total_episodes']}")
        
        if 'output_path' in result:
            print(f"  完整版视频: {os.path.basename(result['output_path'])}")
        elif 'main_output_path' in result:
            print(f"  合并版视频: {os.path.basename(result['main_output_path'])}")
        
        if 'individual_videos' in result:
            print(f"  分集视频 ({len(result['individual_videos'])}个):")
            for video in result['individual_videos']:
                print(f"    第{video['episode']:02d}集: {video['name']}")
    
    # 测试统计逻辑
    print("\n=== 统计测试 ===")
    
    success_count = sum(1 for r in mock_results if r.get('success', False))
    total_count = len(mock_results)
    
    # 统计生成的视频文件
    total_videos = 0
    individual_videos = 0
    merged_videos = 0
    
    for r in mock_results:
        if r.get('success', False):
            # 统计合并视频
            if 'output_path' in r or 'main_output_path' in r:
                merged_videos += 1
            
            # 统计单独视频
            if 'individual_videos' in r:
                individual_videos += len(r['individual_videos'])
    
    total_videos = merged_videos + individual_videos
    
    print(f"成功处理的小说项目: {success_count}/{total_count}")
    print(f"生成的合并视频: {merged_videos} 个")
    print(f"生成的分集视频: {individual_videos} 个")
    print(f"总视频文件数: {total_videos} 个")
    
    # 测试结果消息生成
    if success_count == total_count:
        if settings['enable_merge']:
            result_msg = f"全部完成！成功处理 {success_count} 个小说项目，生成 {merged_videos} 个合并视频和 {individual_videos} 个分集视频"
            detail_msg = f"视频合成完成！\n成功处理 {success_count} 个小说项目\n生成 {merged_videos} 个合并视频\n生成 {individual_videos} 个分集视频"
        else:
            result_msg = f"全部完成！成功处理 {success_count} 个小说项目，生成 {total_videos} 个视频文件"
            detail_msg = f"视频合成完成！\n成功处理 {success_count} 个小说项目\n生成 {total_videos} 个视频文件"
        
        print(f"\n状态消息: {result_msg}")
        print(f"详细消息: {detail_msg.replace(chr(10), ' | ')}")
    
    print("\n=== 日志输出测试 ===")
    
    # 模拟日志输出
    for r in mock_results:
        if r.get('success', False):
            novel_name = r.get('novel_name', '未知')
            if 'individual_videos' in r:
                print(f"✅ {novel_name} - 生成了 {len(r['individual_videos'])} 个分集视频:")
                for video in r['individual_videos']:
                    print(f"   📹 第{video['episode']:02d}集: {video['name']}")
    
    print("\n=== 文件命名规则测试 ===")
    
    # 测试文件命名规则
    test_cases = [
        ("测试小说", 1, "测试小说_01.mp4"),
        ("测试小说", 10, "测试小说_10.mp4"),
        ("测试小说", 99, "测试小说_99.mp4"),
        ("长篇小说名称", 5, "长篇小说名称_05.mp4"),
    ]
    
    print("分集视频命名规则:")
    for novel_name, episode, expected in test_cases:
        actual = f"{novel_name}_{episode:02d}.mp4"
        status = "✅" if actual == expected else "❌"
        print(f"  {status} 小说: {novel_name}, 第{episode}集 -> {actual}")
    
    print("\n=== 输出目录结构示例 ===")
    
    # 示例输出目录结构
    example_structure = """
输出目录/
├── 测试小说1/
│   ├── 测试小说1_完整版.mp4      (合并的完整版视频)
│   ├── 测试小说1_01.mp4         (第1集单独视频)
│   ├── 测试小说1_02.mp4         (第2集单独视频)
│   └── 测试小说1_03.mp4         (第3集单独视频)
└── 测试小说2/
    ├── 测试小说2_合并版.mp4      (合并版视频)
    ├── 测试小说2_01.mp4         (第1集单独视频)
    └── 测试小说2_02.mp4         (第2集单独视频)
    """
    
    print(example_structure)
    
    print("=== 功能优势 ===")
    
    advantages = [
        "✅ 用户可以获得完整的合并视频，方便连续观看",
        "✅ 同时保留单独的分集视频，方便分享和管理",
        "✅ 支持两种合并模式：完整合并和与原视频合并",
        "✅ 统一的文件命名规则，便于识别和排序",
        "✅ 详细的处理日志，清楚显示生成的文件信息",
        "✅ 智能统计功能，准确显示生成的视频数量"
    ]
    
    for advantage in advantages:
        print(advantage)
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    print("单独分集视频输出功能测试\n")
    
    try:
        success = test_individual_videos_output()
        
        if success:
            print("\n🎉 所有测试完成！功能正常工作。")
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
